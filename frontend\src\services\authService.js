import api, { setTokens, clearTokens } from './api';

const AuthService = {
  // Login user
  login: async (username, password) => {
    try {
      const response = await api.post('/api/auth/login', {
        employee_number: username,
        password
      });

      // Store JWT tokens if login successful
      if (response.data.access_token) {
        setTokens(response.data.access_token, response.data.refresh_token);
      }

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Register new user
  register: async (userData) => {
    try {
      const response = await api.post('/api/auth/register', userData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Logout user
  logout: async () => {
    try {
      const response = await api.post('/api/auth/logout');
      // Clear stored tokens
      clearTokens();
      return response.data;
    } catch (error) {
      // Clear tokens even if logout request fails
      clearTokens();
      throw error;
    }
  },

  // Get current user info
  getCurrentUser: async () => {
    try {
      const response = await api.get('/api/auth/me');
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Check if user is authenticated
  isAuthenticated: async () => {
    try {
      const response = await api.get('/api/auth/status');
      return response.data.authenticated;
    } catch (error) {
      return false;
    }
  }
};

export default AuthService;
