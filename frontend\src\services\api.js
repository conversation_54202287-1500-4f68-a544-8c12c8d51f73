import axios from 'axios';

// Token management utilities
const TOKEN_STORAGE_KEY = 'access_token';
const REFRESH_TOKEN_STORAGE_KEY = 'refresh_token';

const getAccessToken = () => localStorage.getItem(TOKEN_STORAGE_KEY);
const getRefreshToken = () => localStorage.getItem(REFRESH_TOKEN_STORAGE_KEY);
const setTokens = (accessToken, refreshToken) => {
  localStorage.setItem(TOKEN_STORAGE_KEY, accessToken);
  if (refreshToken) {
    localStorage.setItem(REFRESH_TOKEN_STORAGE_KEY, refreshToken);
  }
};
const clearTokens = () => {
  localStorage.removeItem(TOKEN_STORAGE_KEY);
  localStorage.removeItem(REFRESH_TOKEN_STORAGE_KEY);
};

// Create an axios instance with default config
const api = axios.create({
  baseURL: '/api', // Use relative URL to work with Vite proxy
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Keep for compatibility
});

// Request interceptor for adding JWT auth token
api.interceptors.request.use(
  (config) => {
    // Add JWT token to Authorization header
    const token = getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Only log non-GET requests in development environment or when debugging is enabled
    if (config.method.toUpperCase() !== 'GET' &&
        (process.env.NODE_ENV === 'development' || process.env.REACT_APP_DEBUG_MODE === 'true')) {
      console.log(`API Request [${config.method.toUpperCase()}] ${config.url}:`, config.data);
    }
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors and token refresh
api.interceptors.response.use(
  (response) => {
    // Only log responses in development environment or when debugging is enabled
    if (process.env.NODE_ENV === 'development' || process.env.REACT_APP_DEBUG_MODE === 'true') {
      console.log(`API Response [${response.config.method.toUpperCase()}] ${response.config.url}:`, response.data);
    }

    return response;
  },
  async (error) => {
    // Handle common errors
    console.error(`API Error [${error.config?.method?.toUpperCase()}] ${error.config?.url}:`, error.response?.data || error.message);

    if (error.response) {
      // Server responded with error status
      if (error.response.status === 401) {
        // Try to refresh token first
        const refreshToken = getRefreshToken();
        if (refreshToken && !error.config._retry) {
          error.config._retry = true;

          try {
            const response = await axios.post('/api/auth/refresh', {
              refresh_token: refreshToken
            });

            const { access_token, refresh_token: newRefreshToken } = response.data;
            setTokens(access_token, newRefreshToken);

            // Retry the original request with new token
            error.config.headers.Authorization = `Bearer ${access_token}`;
            return api.request(error.config);
          } catch (refreshError) {
            // Refresh failed, clear tokens and redirect to login
            clearTokens();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        } else {
          // No refresh token or refresh already tried, clear tokens and redirect
          clearTokens();
          window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

// Export API instance and token management functions
export default api;
export { getAccessToken, getRefreshToken, setTokens, clearTokens };
