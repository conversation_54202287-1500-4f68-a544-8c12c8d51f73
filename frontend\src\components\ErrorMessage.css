/**
 * ErrorMessage Component Styles
 */

.error-message-container {
  margin-bottom: 1rem;
}

.error-message-alert {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.error-message-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.error-message-header {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.error-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.error-text {
  flex: 1;
}

.error-title {
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.error-user-message {
  margin-bottom: 0;
  line-height: 1.5;
}

.error-message-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  align-items: center;
}

.error-action-button {
  min-width: 80px;
}

.error-details-toggle {
  font-size: 0.875rem;
}

.error-technical-details {
  margin-top: 0.5rem;
}

.technical-info {
  font-size: 0.875rem;
}

.info-row {
  margin-bottom: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.info-row strong {
  min-width: 100px;
  color: #495057;
}

.error-stack {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.75rem;
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
  margin-top: 0.5rem;
}

/* Size variations */
.error-message-small .error-icon {
  font-size: 1.25rem;
}

.error-message-small .error-title {
  font-size: 1rem;
}

.error-message-small .error-user-message {
  font-size: 0.875rem;
}

.error-message-large .error-icon {
  font-size: 2rem;
}

.error-message-large .error-title {
  font-size: 1.25rem;
}

.error-message-large .error-user-message {
  font-size: 1.1rem;
}

/* Responsive design */
@media (max-width: 576px) {
  .error-message-header {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .error-icon {
    align-self: flex-start;
  }
  
  .error-message-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .error-action-button,
  .error-details-toggle {
    width: 100%;
  }
  
  .info-row {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .info-row strong {
    min-width: auto;
  }
}

/* Animation for showing/hiding */
.error-message-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus styles for accessibility */
.error-action-button:focus,
.error-details-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .error-message-alert {
    border-width: 2px;
  }
  
  .error-stack {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .error-message-container {
    animation: none;
  }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
  .error-stack {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
  
  .info-row strong {
    color: #a0aec0;
  }
}
